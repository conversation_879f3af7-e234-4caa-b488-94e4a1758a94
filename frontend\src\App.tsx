import { Routes, Route } from "react-router";
import { HomePage } from "./pages/HomePage";
import { CreatePage } from "./pages/CreatePage";
import { Box } from "@chakra-ui/react";
import { Navbar } from "@/components/Navbar.tsx";
import { useColorModeValue } from "@/components/ui/color-mode.tsx";
import { Toaster } from "@/components/ui/toaster";

function App() {
  return (
    <>
      <Box minH={"100vh"} bg={useColorModeValue("gray.100", "gray.900")}>
        <Navbar />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/create" element={<CreatePage />} />
        </Routes>
      </Box>
      <Toaster />
    </>
  );
}

export default App;
