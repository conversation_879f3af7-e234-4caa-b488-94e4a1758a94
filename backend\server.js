import express from "express"; // Import the Express module.
import dotenv from "dotenv"; // Import the Dotenv module.
import cors from "cors"; // Import the CORS module.
import connectDB from "./config/db.js"; // Import the connectDB function.
import Product from "./models/product.model.js"; // Import the Product model.
import mongoose from "mongoose"; // Import the mongoose module.
import productRoutes from "./routes/product.route.js"; // Import the productRoutes module.

dotenv.config(); // Load environment variables from .env file.

const PORT = process.env.PORT || 5000; // Default port is 5000, you can use any port you want, but make sure it's not being used by another process.

const app = express(); // Create an instance of the Express application.

// CORS configuration
app.use(
  cors({
    origin: ["http://localhost:5173", "http://localhost:3000"], // Allow requests from frontend
    credentials: true, // Allow cookies if needed
  })
);

app.use(express.json()); // Parse JSON bodies.

app.use("/api/products", productRoutes); // Use the productRoutes module.

app.get("/", (req, res) => {
  // Create a route for the root path.
  res.send("Hello World");
});

app.listen(PORT, () => {
  // Start the server and listen on the specified port.
  connectDB(); // Connect to MongoDB.
  console.log(`Server started on port ${PORT}`);
});
